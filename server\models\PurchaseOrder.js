import mongoose from 'mongoose';

const locationCoordinates = {
  '1605 Renaissance Commons Blvd., Boynton Beach, FL 33426': {
    lat: 26.5312,
    lng: -80.0728
  },
  '1500 Gateway Blvd., Boynton Beach, FL 33426': {
    lat: 26.5289,
    lng: -80.0756
  },
  '2000 Corporate Blvd., Boca Raton, FL 33431': {
    lat: 26.3683,
    lng: -80.1289
  },
  '1800 Congress Ave., Delray Beach, FL 33445': {
    lat: 26.4615,
    lng: -80.0728
  },
  '2200 Glades Rd., Boca Raton, FL 33431': {
    lat: 26.3683,
    lng: -80.1289
  },
  '1400 Town Center Cir., Boca Raton, FL 33486': {
    lat: 26.3683,
    lng: -80.1289
  },
  '3000 Yamato Rd., Boca Raton, FL 33434': {
    lat: 26.3683,
    lng: -80.1289
  },
  'Dordrecht, Netherlands': {  // <-- added here
    lat: 51.81009506925188,
    lng: 4.734445781909173
  }
};


const materialItemSchema = new mongoose.Schema({
  material: {
    type: String,
    required: true,
    enum: [
      // Aggregates & Base Materials
      'Crushed Concrete',
      'Crushed Asphalt',
      'ABC (Aggregate Base Course)',
      'Road Base',
      '#57 Stone',
      '#89 Stone',
      'Rip Rap (6"–12")',
      'Pea Gravel',
      'Fill Rock',
      'Oversized Rock',
      // Sand & Soil Products
      'Fill Sand',
      'Mason Sand',
      'Concrete Sand',
      'Screened Sand',
      'Topsoil – Unscreened',
      'Topsoil – Screened',
      'Organic Topsoil',
      'Sandy Loam',
      'Clay – Red',
      'Clay – Gray',
      'Stabilized Sand',
      // Recycled / Specialty Materials
      'Recycled Asphalt Millings',
      'Recycled Concrete Base',
      'RAP (Reclaimed Asphalt Pavement)',
      'Flowable Fill (CLSM)',
      'Muck (Haul-off)',
      'Spoil Dirt',
      'Contaminated Soil',
      'Lime Rock Base',
      'Shell Rock',
      // Landscaping & Decorative
      'Mulch – Natural',
      'Mulch – Red',
      'Mulch – Brown',
      'Mulch – Black',
      'River Rock – Small',
      'River Rock – Medium',
      'River Rock – Large',
      'Lava Rock',
      'Pine Bark Nuggets',
      'Potting Soil Mix',
      // Dust & Fines
      'Rock Fines',
      'Granite Fines',
      'Limestone Dust',
      'Screening Material',
      // Bulk Liquids & Additives
      'Water (Dust Control)',
      'Liquid Lime',
      'Slurry Cement',
      // Haul-Off / Disposal Types
      'Concrete Demo (Broken)',
      'Asphalt Demo',
      'Mixed Construction Debris',
      'Tree Debris / Vegetation',
      'Excess Dirt – Clean',
      'Excess Dirt – Contaminated',
      'Septic Pump-Out'
    ]
  },
  quantityOrdered: {
    type: Number,
    required: true,
    min: 0
  },
  quantityDelivered: {
    type: Number,
    default: 0,
    min: 0
  },
  unit: {
    type: String,
    required: true,
    enum: ['tons', 'cubic yards', 'loads', 'gallons'],
    default: 'tons'
  },
  ratePerUnit: {
    type: Number,
    required: true,
    min: 0
  },
  isOverDelivered: {
    type: Boolean,
    default: false
  }
}, { _id: true });

const deliveryLocationSchema = new mongoose.Schema({
  address: {
    type: String,
    required: true,
    enum: [
      '1500 Gateway Blvd., Boynton Beach, FL 33426',
      '2000 Corporate Blvd., Boca Raton, FL 33431',
      '1800 Congress Ave., Delray Beach, FL 33445',
      '2200 Glades Rd., Boca Raton, FL 33431',
      '1400 Town Center Cir., Boca Raton, FL 33486',
      '3000 Yamato Rd., Boca Raton, FL 33434',
      'Dordrecht, Netherlands'
    ]
  },
  coordinates: {
    lat: { type: Number, required: true },
    lng: { type: Number, required: true }
  },
  materials: [materialItemSchema], // Array of materials and quantities for this location
  status: {
    type: String,
    enum: ['pending', 'completed'],
    default: 'pending'
  },
  completedAt: {
    type: Date,
    default: null
  },
  deliveryPhoto: {
    type: String, // URL or base64 string for the delivery ticket photo
    default: null
  },
  signature: {
    type: String, // Base64 string for the signature
    default: null
  }
}, { _id: true });

const purchaseOrderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    trim: true
  },
  client: {
    type: String,
    required: true,
    trim: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: false,
    trim: true
  },
  pickupLocation: {
    address: {
      type: String,
      required: true,
      enum: [
        '1605 Renaissance Commons Blvd., Boynton Beach, FL 33426',
        'Dordrecht, Netherlands'
      ]
    },
    coordinates: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true }
    }
  },
  deliveryLocations: [deliveryLocationSchema],
  pickupDate: {
    type: Date,
    required: true
  },
  deliveryDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'pickup_completed', 'en_route', 'delivery_in_progress', 'delivered', 'cancelled'],
    default: 'pending'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  acceptedAt: {
    type: Date,
    default: null
  },
  pickupCompletedAt: {
    type: Date,
    default: null
  },
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual field to calculate total hauling rate
purchaseOrderSchema.virtual('totalHaulingRate').get(function() {
  let total = 0;
  if (this.deliveryLocations) {
    this.deliveryLocations.forEach(location => {
      if (location.materials) {
        location.materials.forEach(material => {
          total += material.quantityOrdered * material.ratePerUnit;
        });
      }
    });
  }
  return total;
});

// Pre-save middleware to set coordinates based on addresses
purchaseOrderSchema.pre('save', function(next) {
  // Set pickup location coordinates
  if (this.pickupLocation && this.pickupLocation.address) {
    const coords = locationCoordinates[this.pickupLocation.address];
    if (coords) {
      this.pickupLocation.coordinates = coords;
    }
  }

  // Set delivery location coordinates
  if (this.deliveryLocations && this.deliveryLocations.length > 0) {
    this.deliveryLocations.forEach(location => {
      if (location.address && !location.coordinates.lat) {
        const coords = locationCoordinates[location.address];
        if (coords) {
          location.coordinates = coords;
        }
      }
    });
  }

  next();
});

// Generate order number before saving
purchaseOrderSchema.pre('save', async function(next) {
  if (!this.orderNumber) {
    try {
      // Get the count of existing documents
      const count = await this.constructor.countDocuments();
      this.orderNumber = `PO-${String(count + 1).padStart(6, '0')}`;
      console.log('Generated order number:', this.orderNumber);
    } catch (error) {
      console.error('Error generating order number:', error);
      return next(error);
    }
  }
  next();
});

// Update acceptedAt when status changes to accepted
purchaseOrderSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'accepted' && !this.acceptedAt) {
    this.acceptedAt = new Date();
  }
  if (this.isModified('status') && this.status === 'pickup_completed' && !this.pickupCompletedAt) {
    this.pickupCompletedAt = new Date();
  }
  next();
});

// Utility function to calculate distance between two coordinates (Haversine formula)
purchaseOrderSchema.statics.calculateDistance = function(lat1, lng1, lat2, lng2) {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Distance in kilometers
};

// Method to check if trucker is within geofence for pickup
purchaseOrderSchema.methods.isWithinPickupGeofence = function(truckerLat, truckerLng, radiusKm = 1) {
  if (!this.pickupLocation || !this.pickupLocation.coordinates) {
    return false;
  }
  const distance = this.constructor.calculateDistance(
    truckerLat,
    truckerLng,
    this.pickupLocation.coordinates.lat,
    this.pickupLocation.coordinates.lng
  );
  return distance <= radiusKm;
};

// Method to check if trucker is within geofence for a specific delivery location
purchaseOrderSchema.methods.isWithinDeliveryGeofence = function(deliveryLocationId, truckerLat, truckerLng, radiusKm = 1) {
  const deliveryLocation = this.deliveryLocations.id(deliveryLocationId);
  if (!deliveryLocation || !deliveryLocation.coordinates) {
    return false;
  }
  const distance = this.constructor.calculateDistance(
    truckerLat,
    truckerLng,
    deliveryLocation.coordinates.lat,
    deliveryLocation.coordinates.lng
  );
  return distance <= radiusKm;
};

// Export location coordinates for use in other files
export { locationCoordinates };

const PurchaseOrder = mongoose.model('PurchaseOrder', purchaseOrderSchema);

export default PurchaseOrder;
