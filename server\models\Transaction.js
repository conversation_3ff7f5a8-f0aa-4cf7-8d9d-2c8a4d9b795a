import mongoose from 'mongoose';

const deliveredMaterialSchema = new mongoose.Schema({
  material: {
    type: String,
    required: true
  },
  quantityDelivered: {
    type: Number,
    required: true,
    min: 0
  },
  unit: {
    type: String,
    required: true,
    enum: ['tons', 'cubic yards', 'loads', 'gallons']
  },
  ratePerUnit: {
    type: Number,
    required: true,
    min: 0
  }
}, { _id: false });

const transactionSchema = new mongoose.Schema({
  purchaseOrderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PurchaseOrder',
    required: true
  },
  orderNumber: {
    type: String,
    required: true
  },
  trucker: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  deliveryLocationId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  deliveryAddress: {
    type: String,
    required: true
  },
  materialsDelivered: [deliveredMaterialSchema],
  deliveryPhoto: {
    type: String,
    required: true
  },
  signature: {
    type: String,
    required: true
  },
  coordinates: {
    lat: { type: Number, required: true },
    lng: { type: Number, required: true }
  },
  notes: {
    type: String,
    trim: true
  },
  completedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for efficient queries
transactionSchema.index({ trucker: 1, completedAt: -1 });
transactionSchema.index({ purchaseOrderId: 1 });

const Transaction = mongoose.model('Transaction', transactionSchema);

export default Transaction;
