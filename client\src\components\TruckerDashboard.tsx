import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import SignatureCanvas from 'react-signature-canvas';
import LanguageSwitcher from './LanguageSwitcher';

interface MaterialItem {
  _id: string;
  material: string;
  quantityOrdered: number;
  quantityDelivered: number;
  unit: string;
  isOverDelivered: boolean;
}

interface DeliveryLocation {
  _id: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  materials: MaterialItem[];
  status: 'pending' | 'completed';
  completedAt?: string;
  deliveryPhoto?: string;
  signature?: string;
}

interface PurchaseOrder {
  _id: string;
  orderNumber: string;
  client: string;
  title: string;
  description?: string;
  pickupLocation: {
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  } | string; // Support both old and new format
  deliveryLocations: DeliveryLocation[];
  pickupDate: string;
  deliveryDate: string;
  weight: number;
  haulingRate: number;
  status: string;
  createdBy: {
    username: string;
    firstName: string;
    lastName: string;
  };
  assignedTo?: {
    username: string;
    firstName: string;
    lastName: string;
  };
  acceptedAt?: string;
  createdAt: string;
  notes?: string;
}

interface Transaction {
  _id: string;
  purchaseOrderId: string;
  orderNumber: string;
  trucker: {
    _id: string;
    username: string;
    firstName: string;
    lastName: string;
  };
  deliveryLocationId: string;
  deliveryAddress: string;
  materialsDelivered: {
    material: string;
    quantityDelivered: number;
    unit: string;
  }[];
  deliveryPhoto: string;
  signature: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  notes?: string;
  completedAt: string;
  createdAt: string;
}

const TruckerDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { t } = useTranslation();
  const [orders, setOrders] = useState<PurchaseOrder[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<PurchaseOrder | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<{lat: number, lng: number} | null>(null);
  const [locationError, setLocationError] = useState<string>('');
  const [showDeliveryModal, setShowDeliveryModal] = useState(false);
  const [selectedDeliveryLocation, setSelectedDeliveryLocation] = useState<DeliveryLocation | null>(null);
  const [deliveryPhoto, setDeliveryPhoto] = useState<string>('');
  const [signature, setSignature] = useState<string>('');
  const [actualQuantities, setActualQuantities] = useState<string[]>([]);
  const [deliveryNotes, setDeliveryNotes] = useState<string>('');
  const signatureRef = useRef<SignatureCanvas>(null);
  const [developerMode, setDeveloperMode] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'orders' | 'transactions'>('orders');

  const API_BASE_URL = '/api';

  useEffect(() => {
    fetchOrders();
    getCurrentLocation();
    if (activeTab === 'transactions') {
      fetchTransactions();
    }
  }, [activeTab]);

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setLocationError('');
        },
        (error) => {
          setLocationError('Unable to get your location. Please enable location services.');
          console.error('Geolocation error:', error);
        }
      );
    } else {
      setLocationError('Geolocation is not supported by this browser.');
    }
  };

  const fetchOrders = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.data.success) {
        setOrders(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTransactions = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders/transactions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.data.success) {
        setTransactions(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
  };

  const handleAcceptOrder = async (orderId: string) => {
    if (!developerMode && !currentLocation) {
      alert('Location is required to accept an order. Please enable location services and try again.');
      getCurrentLocation();
      return;
    }

    try {
      const response = await axios.patch(`${API_BASE_URL}/purchase-orders/${orderId}/accept`, {
        latitude: currentLocation?.lat || 0,
        longitude: currentLocation?.lng || 0,
        developerMode
      });

      if (response.data.success) {
        // Update the orders list
        setOrders(orders.map(order =>
          order._id === orderId
            ? { ...order, status: 'accepted', assignedTo: user ? {
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName
              } : undefined, acceptedAt: new Date().toISOString() }
            : order
        ));
        setShowModal(false);
        setSelectedOrder(null);
        // Refresh orders to get updated view
        fetchOrders();
      }
    } catch (error: any) {
      console.error('Error accepting order:', error);
      const errorMessage = error.response?.data?.message || 'Error accepting order';
      alert(errorMessage);
    }
  };

  const handleCompletePickup = async (orderId: string) => {
    try {
      const response = await axios.patch(`${API_BASE_URL}/purchase-orders/${orderId}/complete-pickup`);

      if (response.data.success) {
        fetchOrders(); // Refresh orders
        alert('Pickup completed successfully!');
      }
    } catch (error: any) {
      console.error('Error completing pickup:', error);
      const errorMessage = error.response?.data?.message || 'Error completing pickup';
      alert(errorMessage);
    }
  };

  const handleCompleteDelivery = async () => {
    if (!selectedDeliveryLocation || (!developerMode && !currentLocation)) {
      alert('Missing required information');
      return;
    }

    if (!deliveryPhoto || !signature) {
      alert('Please provide both a delivery photo and signature');
      return;
    }

    if (!actualQuantities || actualQuantities.length === 0) {
      alert('Please specify the actual quantities delivered');
      return;
    }

    // Validate actual quantities
    for (let i = 0; i < actualQuantities.length; i++) {
      const qty = actualQuantities[i];
      if (!qty || parseFloat(qty) < 0) {
        alert('Please enter valid quantities for all materials (0 or greater)');
        return;
      }
    }

    // Find the active order that contains this delivery location
    const activeOrder = orders.find(order =>
      order.assignedTo?.username === user?.username &&
      ['accepted', 'pickup_completed', 'en_route', 'delivery_in_progress'].includes(order.status)
    );

    if (!activeOrder) {
      alert('No active order found');
      return;
    }

    try {
      const response = await axios.patch(
        `${API_BASE_URL}/purchase-orders/${activeOrder._id}/complete-delivery/${selectedDeliveryLocation._id}`,
        {
          latitude: currentLocation?.lat || 0,
          longitude: currentLocation?.lng || 0,
          deliveryPhoto,
          signature,
          actualQuantities,
          notes: deliveryNotes,
          developerMode
        }
      );

      if (response.data.success) {
        let message = response.data.message;
        if (response.data.warnings && response.data.warnings.length > 0) {
          message += '\n\nWarnings:\n' + response.data.warnings.join('\n');
        }
        alert(message);
        fetchOrders(); // Refresh orders
        setShowDeliveryModal(false);
        setSelectedDeliveryLocation(null);
        setDeliveryPhoto('');
        setSignature('');
        setActualQuantities([]);
        setDeliveryNotes('');
        if (signatureRef.current) {
          signatureRef.current.clear();
        }
      }
    } catch (error: any) {
      console.error('Error completing delivery:', error);
      const errorMessage = error.response?.data?.message || 'Error completing delivery';
      alert(errorMessage);
    }
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setDeliveryPhoto(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const clearSignature = () => {
    if (signatureRef.current) {
      signatureRef.current.clear();
    }
    setSignature('');
  };

  const saveSignature = () => {
    if (signatureRef.current) {
      const signatureData = signatureRef.current.toDataURL();
      setSignature(signatureData);
    }
  };

  // Initialize actual quantities when delivery location is selected
  const handleDeliveryLocationSelect = (location: DeliveryLocation) => {
    setSelectedDeliveryLocation(location);
    // Initialize actual quantities array based on materials in the order
    if (location.materials && location.materials.length > 0) {
      setActualQuantities(new Array(location.materials.length).fill(''));
    } else {
      setActualQuantities(['']);
    }
    setShowDeliveryModal(true);
  };

  const updateActualQuantity = (index: number, value: string) => {
    const updated = [...actualQuantities];
    updated[index] = value;
    setActualQuantities(updated);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-warning';
      case 'accepted': return 'bg-success';
      case 'pickup_completed': return 'bg-info';
      case 'en_route': return 'bg-info';
      case 'delivery_in_progress': return 'bg-primary';
      case 'delivered': return 'bg-success';
      case 'cancelled': return 'bg-danger';
      default: return 'bg-secondary';
    }
  };

  const getPickupLocationAddress = (pickupLocation: any): string => {
    if (typeof pickupLocation === 'string') {
      return pickupLocation;
    }
    return pickupLocation?.address || 'Unknown location';
  };

  const getDeliveryLocationsDisplay = (deliveryLocations: DeliveryLocation[]): string => {
    if (!deliveryLocations || deliveryLocations.length === 0) {
      return 'No delivery locations';
    }
    if (deliveryLocations.length === 1) {
      return deliveryLocations[0].address;
    }
    return `${deliveryLocations.length} locations`;
  };

  const pendingOrders = orders.filter(order => order.status === 'pending');
  const myOrders = orders.filter(order => order.assignedTo?.username === user?.username);
  const hasActiveOrder = myOrders.some(order => ['accepted', 'pickup_completed', 'en_route', 'delivery_in_progress'].includes(order.status));

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <nav className="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div className="container-fluid">
          <span className="navbar-brand">
            <i className="bi bi-truck me-2"></i>
            Trucker Dashboard
          </span>
          <div className="navbar-nav ms-auto d-flex align-items-center">
            <div className="form-check form-switch me-3">
              <input
                className="form-check-input"
                type="checkbox"
                id="developerMode"
                checked={developerMode}
                onChange={(e) => setDeveloperMode(e.target.checked)}
              />
              <label className="form-check-label text-white" htmlFor="developerMode">
                <small>Developer Mode</small>
              </label>
            </div>
            {developerMode && (
              <small className="badge bg-warning text-dark me-3">
                <i className="bi bi-exclamation-triangle me-1"></i>
                Geofencing Disabled
              </small>
            )}
            <span className="navbar-text me-3">
              {t('dashboard.welcome')}, {user?.firstName} {user?.lastName}
            </span>
            <div className="me-3">
              <LanguageSwitcher />
            </div>
            <button className="btn btn-outline-light" onClick={logout}>
              <i className="bi bi-box-arrow-right me-1"></i>
              {t('auth.logout')}
            </button>
          </div>
        </div>
      </nav>

      <div className="container-fluid">
        {/* Statistics */}
        <div className="row mb-4">
          <div className="col-md-4">
            <div className="card bg-warning text-white">
              <div className="card-body">
                <h5>Available Orders</h5>
                <h3>{pendingOrders.length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card bg-success text-white">
              <div className="card-body">
                <h5>My Active Orders</h5>
                <h3>{myOrders.filter(o => o.status !== 'delivered').length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card bg-primary text-white">
              <div className="card-body">
                <h5>Completed Orders</h5>
                <h3>{myOrders.filter(o => o.status === 'delivered').length}</h3>
              </div>
            </div>
          </div>
        </div>

        {/* Current Location Display */}
        <div className="row mb-4">
          <div className="col">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">
                  <i className="bi bi-geo-alt me-2"></i>
                  Current Location
                </h6>
              </div>
              <div className="card-body">
                {locationError ? (
                  <div className="alert alert-warning" role="alert">
                    <i className="bi bi-exclamation-triangle me-2"></i>
                    {locationError}
                    <button className="btn btn-sm btn-outline-warning ms-2" onClick={getCurrentLocation}>
                      Enable Location
                    </button>
                  </div>
                ) : currentLocation ? (
                  <div className="row">
                    <div className="col-md-6">
                      <small className="text-muted">Latitude:</small>
                      <div className="fw-bold">{currentLocation.lat.toFixed(6)}</div>
                    </div>
                    <div className="col-md-6">
                      <small className="text-muted">Longitude:</small>
                      <div className="fw-bold">{currentLocation.lng.toFixed(6)}</div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                    <small className="text-muted">Getting your location...</small>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="row mb-4">
          <div className="col">
            <ul className="nav nav-tabs">
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'orders' ? 'active' : ''}`}
                  onClick={() => setActiveTab('orders')}
                >
                  <i className="bi bi-truck me-2"></i>
                  Orders
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'transactions' ? 'active' : ''}`}
                  onClick={() => setActiveTab('transactions')}
                >
                  <i className="bi bi-receipt me-2"></i>
                  My Transactions
                </button>
              </li>
            </ul>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'orders' && (
          <>
            {/* Available Orders or Active Order */}
            <div className="row mb-4">
              <div className="col">
                <div className="card">
                  <div className="card-header">
                    <h5 className="mb-0">
                      <i className="bi bi-list-ul me-2"></i>
                      {hasActiveOrder ? 'My Active Order' : 'Available Orders'}
                    </h5>
                  </div>
                  <div className="card-body">

                {hasActiveOrder ? (
                  // Show active order details
                  myOrders.filter(order => ['accepted', 'pickup_completed', 'en_route', 'delivery_in_progress'].includes(order.status)).map((order) => (
                    <div key={order._id} className="border rounded p-3">
                      <div className="row">
                        <div className="col-md-8">
                          <h6>{order.orderNumber} - {order.title}</h6>
                          <p><strong>Client:</strong> {order.client}</p>

                          <div className="mb-3">
                            <strong>Pickup Location:</strong>
                            <div className="ms-3">
                              <div>{getPickupLocationAddress(order.pickupLocation)}</div>
                              {order.pickupLocation && typeof order.pickupLocation === 'object' && order.pickupLocation.coordinates && (
                                <small className="text-muted">
                                  📍 {order.pickupLocation.coordinates.lat.toFixed(4)}, {order.pickupLocation.coordinates.lng.toFixed(4)}
                                </small>
                              )}
                            </div>
                          </div>

                          <div className="mb-3">
                            <strong>Delivery Locations:</strong>
                            <div className="ms-3">
                              {order.deliveryLocations?.map((location, index) => (
                                <div key={location._id || index} className="border rounded p-2 mb-2">
                                  <div className="d-flex justify-content-between align-items-start">
                                    <div className="flex-grow-1">
                                      <div className="d-flex align-items-center mb-1">
                                        <span className={`badge ${location.status === 'completed' ? 'bg-success' : 'bg-secondary'} me-2`}>
                                          {location.status === 'completed' ? '✓' : index + 1}
                                        </span>
                                        <strong>{location.address}</strong>
                                      </div>
                                      {location.coordinates && (
                                        <small className="text-muted d-block">
                                          📍 {location.coordinates.lat.toFixed(4)}, {location.coordinates.lng.toFixed(4)}
                                        </small>
                                      )}
                                      {location.materials && location.materials.length > 0 && (
                                        <div className="mt-2">
                                          <small className="text-muted">Materials:</small>
                                          <ul className="list-unstyled ms-2">
                                            {location.materials.map((material, matIndex) => (
                                              <li key={matIndex} className="small">
                                                • {material.quantityDelivered || 0}/{material.quantityOrdered} {material.unit} of {material.material}
                                                {material.isOverDelivered && <span className="text-warning ms-1">⚠️</span>}
                                              </li>
                                            ))}
                                          </ul>
                                        </div>
                                      )}
                                    </div>
                                    {location.status === 'pending' && (currentLocation || developerMode) && (
                                      <button
                                        className="btn btn-sm btn-primary ms-2"
                                        onClick={() => handleDeliveryLocationSelect(location)}
                                      >
                                        Complete Delivery
                                      </button>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="col-md-4">
                          <span className={`badge ${getStatusBadgeClass(order.status)} mb-2`}>
                            {order.status.replace('_', ' ').toUpperCase()}
                          </span>
                          <p><strong>Weight:</strong> {order.weight.toLocaleString()} lbs</p>
                          <p><strong>Rate:</strong> ${order.haulingRate.toLocaleString()}</p>
                          {order.status === 'accepted' && (
                            <button
                              className="btn btn-success btn-sm"
                              onClick={() => handleCompletePickup(order._id)}
                            >
                              Complete Pickup
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  // Show available orders
                  pendingOrders.length === 0 ? (
                    <p className="text-muted">No pending orders available.</p>
                  ) : (
                    <div className="table-responsive">
                      <table className="table table-hover">
                        <thead>
                          <tr>
                            <th>Order #</th>
                            <th>Client</th>
                            <th>Title</th>
                            <th>Route</th>
                            <th>Pickup Date</th>
                            <th>Hauling Rate</th>
                            <th>Weight</th>
                            <th>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          {pendingOrders.map((order) => (
                            <tr key={order._id}>
                              <td>{order.orderNumber}</td>
                              <td>{order.client}</td>
                              <td>{order.title}</td>
                              <td>
                                {getPickupLocationAddress(order.pickupLocation)} → {getDeliveryLocationsDisplay(order.deliveryLocations)}
                              </td>
                              <td>{new Date(order.pickupDate).toLocaleDateString()}</td>
                              <td>${order.haulingRate.toLocaleString()}</td>
                              <td>{order.weight.toLocaleString()} lbs</td>
                              <td>
                                <button
                                  className="btn btn-primary btn-sm"
                                  onClick={() => {
                                    setSelectedOrder(order);
                                    setShowModal(true);
                                  }}
                                >
                                  View Details
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>

        {/* My Orders */}
        <div className="row">
          <div className="col">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">
                  <i className="bi bi-truck me-2"></i>
                  My Orders
                </h5>
              </div>
              <div className="card-body">
                {myOrders.length === 0 ? (
                  <p className="text-muted">You haven't accepted any orders yet.</p>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th>Order #</th>
                          <th>Client</th>
                          <th>Title</th>
                          <th>Route</th>
                          <th>Status</th>
                          <th>Pickup Date</th>
                          <th>Delivery Date</th>
                          <th>Hauling Rate</th>
                        </tr>
                      </thead>
                      <tbody>
                        {myOrders.map((order) => (
                          <tr key={order._id}>
                            <td>{order.orderNumber}</td>
                            <td>{order.client}</td>
                            <td>{order.title}</td>
                            <td>
                              {getPickupLocationAddress(order.pickupLocation)} → {getDeliveryLocationsDisplay(order.deliveryLocations)}
                            </td>
                            <td>
                              <span className={`badge ${getStatusBadgeClass(order.status)}`}>
                                {order.status.replace('_', ' ').toUpperCase()}
                              </span>
                            </td>
                            <td>{new Date(order.pickupDate).toLocaleDateString()}</td>
                            <td>{new Date(order.deliveryDate).toLocaleDateString()}</td>
                            <td>${order.haulingRate.toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
          </>
        )}

        {/* Transactions Tab */}
        {activeTab === 'transactions' && (
          <div className="card">
            <div className="card-body">
              <h5 className="card-title">
                <i className="bi bi-receipt me-2"></i>
                My Transaction History
              </h5>
              <div className="table-responsive">
                <table className="table table-striped">
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>Order #</th>
                      <th>Delivery Address</th>
                      <th>Materials Delivered</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="text-center text-muted">
                          No transactions found
                        </td>
                      </tr>
                    ) : (
                      transactions.map((transaction) => (
                        <tr key={transaction._id}>
                          <td>
                            <div>{new Date(transaction.completedAt).toLocaleDateString()}</div>
                            <small className="text-muted">{new Date(transaction.completedAt).toLocaleTimeString()}</small>
                          </td>
                          <td>{transaction.orderNumber}</td>
                          <td>{transaction.deliveryAddress}</td>
                          <td>
                            {transaction.materialsDelivered.map((material, index) => (
                              <div key={index} className="small">
                                {material.quantityDelivered} {material.unit} of {material.material}
                              </div>
                            ))}
                          </td>
                          <td>
                            <button
                              className="btn btn-sm btn-outline-primary me-1"
                              onClick={() => window.open(transaction.deliveryPhoto, '_blank')}
                              title="View Photo"
                            >
                              <i className="bi bi-image"></i>
                            </button>
                            <button
                              className="btn btn-sm btn-outline-secondary"
                              onClick={() => window.open(transaction.signature, '_blank')}
                              title="View Signature"
                            >
                              <i className="bi bi-pen"></i>
                            </button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {showModal && selectedOrder && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Order Details - {selectedOrder.orderNumber}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowModal(false);
                    setSelectedOrder(null);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <div className="row">
                  <div className="col-md-6">
                    <h6>Order Information</h6>
                    <p><strong>Client:</strong> {selectedOrder.client}</p>
                    <p><strong>Title:</strong> {selectedOrder.title}</p>
                    {selectedOrder.description && (
                      <p><strong>Description:</strong> {selectedOrder.description}</p>
                    )}
                    <p><strong>Weight:</strong> {selectedOrder.weight.toLocaleString()} lbs</p>
                    <p><strong>Hauling Rate:</strong> ${selectedOrder.haulingRate.toLocaleString()}</p>
                    {selectedOrder.notes && (
                      <p><strong>Notes:</strong> {selectedOrder.notes}</p>
                    )}
                  </div>
                  <div className="col-md-6">
                    <h6>Dates</h6>
                    <p><strong>Pickup Date:</strong> {new Date(selectedOrder.pickupDate).toLocaleDateString()}</p>
                    <p><strong>Delivery Date:</strong> {new Date(selectedOrder.deliveryDate).toLocaleDateString()}</p>
                    <p><strong>Created:</strong> {new Date(selectedOrder.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>

                <div className="row mt-3">
                  <div className="col-md-6">
                    <h6>Pickup Location</h6>
                    <div>{getPickupLocationAddress(selectedOrder.pickupLocation)}</div>
                    {selectedOrder.pickupLocation && typeof selectedOrder.pickupLocation === 'object' && selectedOrder.pickupLocation.coordinates && (
                      <small className="text-muted">
                        📍 {selectedOrder.pickupLocation.coordinates.lat.toFixed(4)}, {selectedOrder.pickupLocation.coordinates.lng.toFixed(4)}
                      </small>
                    )}
                  </div>
                  <div className="col-md-6">
                    <h6>Delivery Locations</h6>
                    {selectedOrder.deliveryLocations && selectedOrder.deliveryLocations.length > 0 ? (
                      <div>
                        {selectedOrder.deliveryLocations.map((location, index) => (
                          <div key={location._id || index} className="border rounded p-2 mb-2">
                            <div className="d-flex align-items-center mb-1">
                              <small className={`badge ${location.status === 'completed' ? 'bg-success' : 'bg-secondary'} me-2`}>
                                {location.status === 'completed' ? '✓' : index + 1}
                              </small>
                              <strong className="small">{location.address}</strong>
                            </div>
                            {location.coordinates && (
                              <small className="text-muted d-block">
                                📍 {location.coordinates.lat.toFixed(4)}, {location.coordinates.lng.toFixed(4)}
                              </small>
                            )}
                            {location.materials && location.materials.length > 0 && (
                              <div className="mt-1">
                                <small className="text-muted">Materials:</small>
                                <ul className="list-unstyled ms-2">
                                  {location.materials.map((material: any, matIndex: number) => (
                                    <li key={matIndex} className="small">
                                      • {material.quantity} {material.unit} of {material.material}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted">No delivery locations</p>
                    )}
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowModal(false);
                    setSelectedOrder(null);
                  }}
                >
                  Close
                </button>
                {selectedOrder.status === 'pending' && (
                  <button
                    type="button"
                    className="btn btn-success"
                    onClick={() => handleAcceptOrder(selectedOrder._id)}
                  >
                    <i className="bi bi-check-circle me-1"></i>
                    Accept Order
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delivery Completion Modal */}
      {showDeliveryModal && selectedDeliveryLocation && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Complete Delivery</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowDeliveryModal(false);
                    setSelectedDeliveryLocation(null);
                    setDeliveryPhoto('');
                    setSignature('');
                    setActualQuantities([]);
                    setDeliveryNotes('');
                    if (signatureRef.current) {
                      signatureRef.current.clear();
                    }
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <h6>Delivery Location:</h6>
                <p>{selectedDeliveryLocation.address}</p>

                {/* Materials to Deliver Section */}
                <div className="mb-4">
                  <h6 className="mb-3">Materials to Deliver</h6>
                  {selectedDeliveryLocation.materials && selectedDeliveryLocation.materials.length > 0 ? (
                    selectedDeliveryLocation.materials.map((material, index) => (
                      <div key={index} className="row mb-3 align-items-center border rounded p-3">
                        <div className="col-md-4">
                          <label className="form-label fw-bold">Material</label>
                          <div className="form-control-plaintext">{material.material}</div>
                        </div>
                        <div className="col-md-3">
                          <label className="form-label fw-bold">Ordered</label>
                          <div className="form-control-plaintext">{material.quantityOrdered} {material.unit}</div>
                        </div>
                        <div className="col-md-3">
                          <label className="form-label fw-bold">Already Delivered</label>
                          <div className="form-control-plaintext">{material.quantityDelivered || 0} {material.unit}</div>
                        </div>
                        <div className="col-md-2">
                          <label className="form-label fw-bold">Remaining</label>
                          <div className="form-control-plaintext">
                            {(material.quantityOrdered - (material.quantityDelivered || 0)).toFixed(1)} {material.unit}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted">No materials specified for this location.</p>
                  )}
                </div>

                {/* Actual Quantities Delivered Section */}
                <div className="mb-4">
                  <h6 className="mb-3">Actual Quantities Delivered *</h6>
                  {selectedDeliveryLocation.materials && selectedDeliveryLocation.materials.length > 0 ? (
                    selectedDeliveryLocation.materials.map((material, index) => (
                      <div key={index} className="row mb-3 align-items-center">
                        <div className="col-md-6">
                          <label className="form-label">{material.material}</label>
                        </div>
                        <div className="col-md-4">
                          <input
                            type="number"
                            className="form-control"
                            placeholder="0"
                            value={actualQuantities[index] || ''}
                            onChange={(e) => updateActualQuantity(index, e.target.value)}
                            min="0"
                            step="0.1"
                            required
                          />
                        </div>
                        <div className="col-md-2">
                          <span className="form-control-plaintext">{material.unit}</span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted">No materials to deliver.</p>
                  )}
                </div>

                {/* Notes Section */}
                <div className="mb-4">
                  <label className="form-label">Delivery Notes (Optional)</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    value={deliveryNotes}
                    onChange={(e) => setDeliveryNotes(e.target.value)}
                    placeholder="Any additional notes about this delivery..."
                  ></textarea>
                </div>

                <div className="row">
                  <div className="col-md-6">
                    <h6>Upload Delivery Photo *</h6>
                    <input
                      type="file"
                      className="form-control mb-3"
                      accept="image/*"
                      onChange={handlePhotoUpload}
                    />
                    {deliveryPhoto && (
                      <div className="mb-3">
                        <img
                          src={deliveryPhoto}
                          alt="Delivery"
                          style={{ maxWidth: '100%', maxHeight: '200px' }}
                          className="border rounded"
                        />
                      </div>
                    )}
                  </div>

                  <div className="col-md-6">
                    <h6>Digital Signature *</h6>
                    <div className="border rounded mb-2" style={{ height: '200px' }}>
                      <SignatureCanvas
                        ref={signatureRef}
                        canvasProps={{
                          width: 300,
                          height: 200,
                          className: 'signature-canvas'
                        }}
                        onEnd={saveSignature}
                      />
                    </div>
                    <div className="d-flex gap-2">
                      <button
                        className="btn btn-sm btn-outline-secondary"
                        onClick={clearSignature}
                      >
                        Clear Signature
                      </button>
                      {signature && (
                        <small className="text-success align-self-center">✓ Signature captured</small>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowDeliveryModal(false);
                    setSelectedDeliveryLocation(null);
                    setDeliveryPhoto('');
                    setSignature('');
                    setActualQuantities([]);
                    setDeliveryNotes('');
                    if (signatureRef.current) {
                      signatureRef.current.clear();
                    }
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-success"
                  onClick={handleCompleteDelivery}
                  disabled={!deliveryPhoto || !signature}
                >
                  <i className="bi bi-check-circle me-1"></i>
                  Complete Delivery
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TruckerDashboard;
